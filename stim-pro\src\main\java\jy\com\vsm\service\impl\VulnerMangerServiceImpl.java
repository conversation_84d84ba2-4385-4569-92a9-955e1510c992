package jy.com.vsm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.micrometer.core.instrument.util.StringUtils;
import jy.com.vsm.common.ResultEnum;
import jy.com.vsm.common.VulTypeEnum;
import jy.com.vsm.domain.*;
import jy.com.vsm.domain.flow.FlowCodereview;
import jy.com.vsm.domain.vul.VulnerSync;
import jy.com.vsm.repository.*;
import jy.com.vsm.security.SecurityUtils;
import jy.com.vsm.service.*;
import jy.com.vsm.service.dto.*;
import jy.com.vsm.service.page.SearchValue;
import jy.com.vsm.service.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static jdk.nashorn.internal.objects.NativeString.substring;

@Service
@Transactional
public class VulnerMangerServiceImpl implements VulnerMangerService {

    private final Logger log = LoggerFactory.getLogger(VulnerMangerServiceImpl.class);

    @Autowired
    private FlowTemplateService flowTemplateService;

    @Autowired
    private MailService mailService;


    @Autowired
    private UserService userService;

    @Autowired
    private FlowHandleHistoryService flowHandleHistoryService;

    @Autowired
    private IdOrganizerService idOrganizerService;

    @Autowired
    private FlowTodoService flowTodoService;

    private final FlowMangerRepository flowMangerRepository;

    private final AssetAppRepository assetAppRepository;

    private final UserRepository userRepository;

    private final VulnerMangerRepository vulnerMangerRepository;

    private final SafetyDetectionRepository safetyDetectionRepository;

    private final AssetNetworkRepository assetNetworkRepository;

    public VulnerMangerServiceImpl(VulnerMangerRepository vulnerMangerRepository, SafetyDetectionRepository safetyDetectionRepository, FlowMangerRepository flowMangerRepository, AssetAppRepository assetAppRepository, UserRepository userRepository, AssetNetworkRepository assetNetworkRepository) {
        this.vulnerMangerRepository = vulnerMangerRepository;
        this.safetyDetectionRepository = safetyDetectionRepository;
        this.flowMangerRepository = flowMangerRepository;
        this.assetAppRepository = assetAppRepository;
        this.userRepository = userRepository;
        this.assetNetworkRepository = assetNetworkRepository;
    }

    @Override
    public Page<VulnerManger> findAllFilter(int start, int length, String searchValue, SortDTO... sortDTOs) {
        log.info("VulnerMangerServiceImpl to get findAllFilter :searchValue：{},sortDTOs：{}", searchValue, sortDTOs);
        Specification<VulnerManger> specification = new Specification<VulnerManger>() {
            @Override
            public Predicate toPredicate(Root<VulnerManger> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                return cb.or(getSpecificationOr(searchValue, root, cb).toArray(new Predicate[0]));
            }
        };
        Page<VulnerManger> page = null;
        Page<VulnerManger> page2 = null;
        Pageable pageable = ServiceUtil.getPageable(start, length, sortDTOs);
        page = this.vulnerMangerRepository.findAll(specification, pageable);
        page2 = getUserFilterData(page, start, pageable, specification, sortDTOs);
        if (null != page2) {
            return page2;
        }
        return page;
    }


    @Override
    public Page<VulnerManger> findAllFilter(int start, int length, String ggg, SearchValue searchValue, SortDTO... sortDTOs) {
        log.info("VulnerMangerServiceImpl to get findAllFilter : ggg：{},searchValue：{},sortDTOs：{}", ggg, searchValue, sortDTOs);
        Specification<VulnerManger> specification = new Specification<VulnerManger>() {
            @Override
            public Predicate toPredicate(Root<VulnerManger> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                return getSpecification(searchValue, ggg, root, query, cb);
            }
        };
        Page<VulnerManger> page = null;
        Page<VulnerManger> page2 = null;
        Pageable pageable = ServiceUtil.getPageable(start, length, sortDTOs);
        page = this.vulnerMangerRepository.findAll(specification, pageable);
        page2 = getUserFilterData(page, start, pageable, specification, sortDTOs);
        if (null != page2) {
            return page2;
        }
        return page;
    }

    public Page<VulnerManger> getUserFilterData(Page<VulnerManger> page, int start, Pageable pageable, Specification<VulnerManger> specification, SortDTO... sortDTOs) {
        List<VulnerManger> list = new ArrayList<>();
        Set set = new LinkedHashSet();
        String rol = userService.getCurrentUserAllRole();
        Optional<User> user = userRepository.findOneByLogin(SecurityUtils.getCurrentUserLogin());
        List<VulnerManger> roleApproval = new ArrayList<>();
        if (rol.contains("ROLE_ADMIN") || rol.contains("ROLE_SECURITY")) {

            return page;
        } else if (rol.contains("ROLE_APPROVAL") || rol.contains("ROLE_UNIT")) {//审核员 条线领导
            long countTotal = page.getTotalElements();//总条数
            roleApproval = page.getContent();//分页后的数据
            if (countTotal <= 10) {
                roleApproval = roleApproval.stream().filter(vul -> vul.getDept().equals(user.get().getDept())).collect(Collectors.toList());
            } else {
                pageable = ServiceUtil.getPageable(start, (int) countTotal, sortDTOs);
                page = this.vulnerMangerRepository.findAll(specification, pageable);
                roleApproval = page.getContent();
                roleApproval = roleApproval.stream().filter(vul -> vul.getDept().equals(user.get().getDept())).collect(Collectors.toList());
            }
            set.addAll(roleApproval);
        } else {
            List<VulnerManger> roleUser = new ArrayList<>();
            if (rol.contains("ROLE_USER")) {//属主
                long countTotal = page.getTotalElements();//总条数
                roleUser = page.getContent();//分页后的数据
                if (countTotal <= 10) {
                    roleUser = roleUser.stream().filter(vul -> vul.getOwnerId().equals(user.get().getLogin())).collect(Collectors.toList());
                } else {
                    pageable = ServiceUtil.getPageable(start, (int) countTotal, sortDTOs);
                    page = this.vulnerMangerRepository.findAll(specification, pageable);
                    roleUser = page.getContent();
                    roleUser = roleUser.stream().filter(vul -> StringUtils.isNotEmpty(vul.getOwnerId()) && vul.getOwnerId().equals(user.get().getLogin())).collect(Collectors.toList());
                }
                set.addAll(roleUser);
            }
        }
        list.addAll(set);
        page = new PageImpl<VulnerManger>(list);
        return page;
    }

    @Override
    public List<VulnerManger> pageData(Page<VulnerManger> list, int sta, int end) {
        String rol = userService.getCurrentUserAllRole();
        List<VulnerManger> data = list.getContent();
        if (!rol.contains("ROLE_ADMIN")) {//管理员查询全部的数据
            if (data.size() < end) {
                data = data.subList(sta, data.size());
            } else {
                int to = sta + end;
                if (data.size() < to) {
                    data = data.subList(sta, data.size());
                } else {
                    data = data.subList(sta, to);
                }
            }
        }
        return data;
    }

    @Override
    public int findAllFilterTotal(String searchValue) {
        log.info("VulnerMangerServiceImpl to get findAllFilterTotal : searchValue：{}", searchValue);
        List<BigInteger> list = vulnerMangerRepository.findAllFilterTotal(searchValue);
        return new Integer(String.valueOf(list.get(0).longValue()));
    }

    @Override
    public VulnerManger findOne(String id) {
        log.info("VulnerMangerServiceImpl to get findOne : id：{}", id);
        Optional<VulnerManger> assets = vulnerMangerRepository.findById(id);
        return assets.get();
    }

    @Override
    public void delete(String id) {
        log.info("VulnerMangerServiceImpl to get delete : id：{}", id);
        String[] ids = {};
        if (StringUtils.isNotEmpty(id)) {
            if (id.indexOf(",") > 0) {
                ids = id.split(",");
                List<VulnerManger> list = new ArrayList<>();
                for (String idd : ids) {
                    VulnerManger f = new VulnerManger();
                    f.setId(idd);
                    list.add(f);
                }
                if (list.size() > 0) {
                    vulnerMangerRepository.deleteInBatch(list);
                }
            } else {
                vulnerMangerRepository.deleteById(id);
            }
        }
    }

    @Override
    public VulnerManger save(VulnerManger vulnerManger) {
        log.info("VulnerMangerServiceImpl to get save : VulnerManger：{}", vulnerManger);
        if (isEmpty(vulnerManger.getId())) {
            Map<String, Object> map = new HashMap<>();
            AssetApp assetApp = assetAppRepository.findOneByName(vulnerManger.getOwAsset());
            Optional<User> user = userRepository.findOneByLogin(assetApp.getOwnerId());
            map.put("owAsset", vulnerManger.getOwAsset());
            map.put("owner", assetApp.getOwner());
            map.put("ownerId", assetApp.getOwnerId());
            FlowManger flowManger = addFlowMangerGetId(false, map);//获取流程id false手动新增
            vulnerManger.setId(idOrganizerService.getTaskId("漏洞信息"));
            vulnerManger.setOwFlow(flowManger.getId());
            vulnerManger.setCreateTime(ZonedDateTime.now());
            vulnerManger.setStatus("待处理");
            vulnerManger.setDept(user.map(User::getDept).orElse(null));
            vulnerManger.setOwnerId(assetApp.getOwnerId());
            vulnerManger.setOwner(assetApp.getOwner());
            vulnerManger.setEnvironment(setEnvironmentByAssetApp(assetApp.getEnvironment()));
            vulnerManger.setFeedbackTime(String.valueOf(Instant.now().plus(1, ChronoUnit.DAYS)));

            //新增
            flowTodoService.flowMangerTodoSave(flowManger);
            mailOperation(flowManger);//漏洞流程操作记录and发送邮箱
            return vulnerMangerRepository.save(vulnerManger);
        }
        //更新
        return vulnerMangerRepository.save(vulnerManger);
    }

    private String setEnvironmentByAssetApp(String env) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(env)) {
            if (env.contains("互联网")) {
                return "是";
            }
        }
        return "否";
    }

    @Override
    public String save(LoopholeCheck loopholeCheck) {
        Map<String, String> specialMap = new HashMap<>();
        log.info("VulnerMangerServiceImpl to get save : LoopholeCheck：{}", loopholeCheck);
        String[] arr = loopholeCheck.getAssetsName().split(",");
        if ("网络设备漏洞".equals(loopholeCheck.getLoopholeType())) {
            for (int i = 0; i < arr.length; i++) {
                AssetNetwork assetNetwork = assetNetworkRepository.findOneByName(arr[i]);
                String owner = assetNetwork.getOpOwner().substring(0, assetNetwork.getOpOwner().indexOf("/"));
                String name = assetNetwork.getName();
                if (specialMap.containsKey(owner)) {
                    String name2 = specialMap.get(owner);
                    String name3 = name2 + "," + name;
                    specialMap.put(owner, name3);
                } else {
                    specialMap.put(owner, name);
                }
            }
        } else {
            for (int j = 0; j < arr.length; j++) {
                AssetApp assetApp = assetAppRepository.findOneByName(arr[j]);
                String owner = "";
                String name = assetApp.getName();
                switch (loopholeCheck.getInChargeType()) {
                    case "业务":
                        owner = assetApp.getOwnerId();
                        break;
                    case "开发":
                        owner = assetApp.getDevOwnerId();
                        break;
                    case "运维":
                        owner = assetApp.getOpOwnerId();
                        break;
                }
                if (specialMap.containsKey(owner)) {
                    String name2 = specialMap.get(owner);
                    String name3 = name2 + "," + name;
                    specialMap.put(owner, name3);
                } else {
                    specialMap.put(owner, name);
                }
            }
        }
        String loopholeId = "";
        for (String key : specialMap.keySet()) {
            String value = specialMap.get(key);
            loopholeId = checkCssembleLoopholeInfo(loopholeCheck, value, key) + "," + loopholeId;
        }
        return loopholeId;
    }

    @Override
    public String save(FlowSafeNotifyDTO flowSafeNotifyDTO) {
        log.info("VulnerMangerServiceImpl to get save : FlowSafeNotifyDTO：{}", flowSafeNotifyDTO);
        if (null != flowSafeNotifyDTO) {
            List<AssetApp> assetApps = null;
            if (flowSafeNotifyDTO.getFeedbackType().equals("feedback")) {
                assetApps = flowSafeNotifyDTO.getAssetApplicationList();
            } else {
                assetApps = flowSafeNotifyDTO.getAddAssetApplicationList();
            }
            Map<String, Object> map = new HashMap<>();
            Map<String, String> specialMap = new HashMap<>();
            for (AssetApp app : assetApps) {
                String owner = app.getOwnerId();
                String name = app.getName();
                if (specialMap.containsKey(owner)) {
                    String name2 = specialMap.get(owner);
                    String name3 = name2 + "," + name;
                    specialMap.put(owner, name3);
                } else {
                    specialMap.put(owner, name);
                }
            }

            String loopholeId = "";
            for (String key : specialMap.keySet()) {
                String value = specialMap.get(key);
                UserDTO user = userService.findByUserId(flowSafeNotifyDTO.getOwnerId());
                map.put("owAsset", value);
                map.put("ownerId", flowSafeNotifyDTO.getOwnerId());
                map.put("owner", flowSafeNotifyDTO.getOwner());
                map.put("source", flowSafeNotifyDTO.getFlowSource());
                map.put("type", flowSafeNotifyDTO.getFlowType());
                map.put("mangerId", flowSafeNotifyDTO.getCreaterId());
                map.put("manger", flowSafeNotifyDTO.getCreater());
                map.put("checkerId", flowSafeNotifyDTO.getCheckerId());
                map.put("checker", flowSafeNotifyDTO.getChecker());
                map.put("description", flowSafeNotifyDTO.getFlowName());
                map.put("fixDeadlineTime", flowSafeNotifyDTO.getRepairTime());
                FlowManger flowManger = addFlowMangerGetId(false, map);//获取流程id false手动新增
                String[] owAssetArr = value.split(",");
                for (String s : owAssetArr) {
                    VulnerManger vulnerManger = new VulnerManger();
                    vulnerManger.setId(idOrganizerService.getTaskId("漏洞信息"));
                    vulnerManger.setOwAsset(s);
                    vulnerManger.setType(flowSafeNotifyDTO.getFlowType());
                    vulnerManger.setLevel(flowSafeNotifyDTO.getFlowLevel());


                    vulnerManger.setName(flowSafeNotifyDTO.getFlowName());
                    vulnerManger.setReference(flowSafeNotifyDTO.getRefSuggestion());
                    vulnerManger.setDescription(flowSafeNotifyDTO.getInspectionMethod());
                    vulnerManger.setCveId(flowSafeNotifyDTO.getDetailed());
                    vulnerManger.setRepairPlan(flowSafeNotifyDTO.getDisposalSuggestion());


                    vulnerManger.setOwFlow(flowManger.getId());
                    vulnerManger.setCreateTime(ZonedDateTime.now());
                    vulnerManger.setStatus("待处理");
                    vulnerManger.setDept(user.getDept());
                    vulnerManger.setOwnerId(flowSafeNotifyDTO.getOwnerId());
                    vulnerManger.setOwner(flowSafeNotifyDTO.getOwner());
                    //vulnerManger.setFeedbackTime(String.valueOf(Instant.now().plus(1, ChronoUnit.DAYS)));
                    vulnerMangerRepository.save(vulnerManger);
                }
                if (StringUtils.isEmpty(loopholeId)) {
                    loopholeId = flowManger.getId();
                } else {
                    loopholeId = loopholeId + "," + flowManger.getId();
                }

                flowTodoService.flowMangerTodoSave(flowManger);
                mailOperation(flowManger);//漏洞流程操作记录and发送邮箱
            }

            return loopholeId;
        }
        return null;
    }

    @Override
    public List<Map<String, String>> countLeakGroupByCategory() {
        return vulnerMangerRepository.countLeakGroupByCategory();
    }

    @Override
    public List<Map<String, Integer>> countLeakByTypeAndCreateTime(String vulType) {
        return vulnerMangerRepository.countLeakByTypeAndCreateTime(vulType);
    }

    @Override
    public List<Map<String, Object>> countLeakByDept() {
        return vulnerMangerRepository.countLeakByDept();
    }

    @Override
    public List<Map<String, Object>> countLeakDetailByDept(FlowReportDTO flowReportDTO) {
        return vulnerMangerRepository.countLeakDetailByDept(flowReportDTO.getStartTime(), flowReportDTO.getEndTime());
    }

    @Override
    public List<Map<String, Object>> countLeakStatusDetailByDept(FlowReportDTO flowReportDTO) {
        return vulnerMangerRepository.countLeakStatusDetailByDept(flowReportDTO.getStartTime(), flowReportDTO.getEndTime());
    }

    public String parseFlowManagerVulDesc(List<VulnerSync> list, String userName) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> vulnerDescList = list.stream().map(VulnerSync::getHostMessage).collect(Collectors.toList());
        List<String> vulnerDescList2 = vulnerDescList.stream().distinct().collect(Collectors.toList());
        String currentDate = DateUtils.getLongDateStr();
        StringBuilder descBuff = new StringBuilder();
        descBuff.append(currentDate);
        descBuff.append(userName);
        descBuff.append("(平台管理员)标记了风险提示中如下漏洞需要处理，包括: <br />");
        if (vulnerDescList2.size() > 5) {
            List<String> descString = vulnerDescList2.subList(0, 5);
            descBuff.append(descString.stream().map(String::valueOf).collect(Collectors.joining("<br />")));
            descBuff.append("等。</br />");
        } else {
            descBuff.append(vulnerDescList2.stream().map(String::valueOf).collect(Collectors.joining("<br />")));
            descBuff.append("</br />");
        }
        String hostDesc = "本次涉及主机数量：" + list.size() + ", " + "本次漏洞总计： " + list.size();
        descBuff.append(hostDesc);
        return descBuff.toString();
    }

    @Override
    public String save(List<VulnerSync> vulnerSyncList, String assetName, UserDTO userDTO) {
        //待响应数据
        Map<String, Object> map = new HashMap<>();
        AssetApp assetApp = assetAppRepository.findOneByName(assetName);
        //Optional<User> user = userRepository.findOneByLogin(assetApp.getOwnerId());

        map.put("owAsset", assetName);
        map.put("ownerId", userDTO.getLogin());
        map.put("owner", userDTO.getUserName());
        map.put("source", "风险提示");
        map.put("description", parseFlowManagerVulDesc(vulnerSyncList, userDTO.getUserName()));

        FlowManger flowManger = addFlowMangerGetId(false, map);//获取流程id false手动新增
        List<VulnerManger> vulnerMangerList = new ArrayList<>();
        for (VulnerSync vulnerSync : vulnerSyncList) {
            VulnerManger vulnerManger = new VulnerManger();
            vulnerManger.setId(idOrganizerService.getTaskId("漏洞信息"));
            vulnerManger.setOwAsset(assetName);
            vulnerManger.setType(VulTypeEnum.getCode(vulnerSync.getCategory()));
            String level = "";
            switch (vulnerSync.getLevel()) {
                case 1:
                    level = "低危";
                    break;
                case 2:
                    level = "中危";
                    break;
                case 3:
                    level = "高危";
                    break;
                default:
                    level = "严重";
                    break;
            }
            vulnerManger.setLevel(level);
            vulnerManger.setName(vulnerSync.getVulName());
            vulnerManger.setOwFlow(flowManger.getId());
            vulnerManger.setCreateTime(ZonedDateTime.now());
            vulnerManger.setStatus("待处理");
            vulnerManger.setDept(userDTO.getDept());
            vulnerManger.setOwnerId(userDTO.getLogin());
            vulnerManger.setOwner(userDTO.getUserName());
            ZonedDateTime date = ZonedDateTime.now().plusDays(15);
            vulnerManger.setFeedbackTime(DateUtils.parseZonedDateTime(date));
            vulnerManger.setCveId(vulnerSync.getCveId());

            vulnerManger.setDescription(vulnerSync.getDescription());
            vulnerManger.setPath(vulnerSync.getPath());

            vulnerManger.setRepairPlan(vulnerSync.getRepairPlan());
            vulnerManger.setReference(vulnerSync.getReference());
            vulnerManger.setHostIp(vulnerSync.getHostIp());
            vulnerManger.setHostName(vulnerSync.getHostName());
            vulnerManger.setEnvironment(setEnvironmentByAssetApp(assetApp.getEnvironment()));
            vulnerMangerList.add(vulnerManger);
            //vulnerMangerRepository.save(vulnerManger);
        }
        if (CollectionUtils.isNotEmpty(vulnerMangerList)) {
            vulnerMangerRepository.saveAll(vulnerMangerList);
        }
        flowTodoService.flowMangerTodoSave(flowManger);
        mailOperation(flowManger);//漏洞流程操作记录and发送邮箱
        return flowManger.getId();
    }

    @Override
    public VulnerManger updateVulnerManger(String id, String result) {
        VulnerManger vulnerManger = vulnerMangerRepository.findOneById(id);
        String resultDesc = ResultEnum.getDesc(Integer.parseInt(result));
        vulnerManger.setStatus("已处理");
        vulnerManger.setFeedback(resultDesc);
        vulnerManger.setUpdateTime(ZonedDateTime.now());
        vulnerMangerRepository.save(vulnerManger);
        return vulnerManger;
    }

    @Override
    public List<VulnerManger> findByTypeAndCreateTimeBetween(String type, ZonedDateTime startTime, ZonedDateTime endTime) {
        return vulnerMangerRepository.findByTypeAndCreateTimeBetween(type, startTime, endTime);
    }

    @Override
    public List<VulnerManger> getAllVulByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("没有选中漏洞信息，导出失败。");
        }
        return vulnerMangerRepository.findAllById(ids);
    }


    public String checkCssembleLoopholeInfo(LoopholeCheck loopholeCheck, String owAsset, String owner) {
        //漏洞排查组装漏洞信息.
        Map<String, Object> map = new HashMap<>();
        /*AssetApp assetApp=assetAppRepository.findOneByName(vulnerManger.getOwAsset());*/
        Optional<User> user = userRepository.findOneByLogin(owner);
        map.put("owAsset", owAsset);
        map.put("ownerId", user.map(User::getLogin).orElse(null));
        map.put("owner", user.map(User::getUserName).orElse(null));
        map.put("source", "漏洞排查");
        map.put("mangerId", loopholeCheck.getManagerId());
        map.put("manger", loopholeCheck.getManager());
        map.put("checkerId", loopholeCheck.getCheckerId());
        map.put("checker", loopholeCheck.getChecker());
        map.put("description", loopholeCheck.getLoopholeDesc());

        FlowManger flowManger = addFlowMangerGetId(false, map);//获取流程id false手动新增

        String[] owAssetArr = owAsset.split(",");
        for (int i = 0; i < owAssetArr.length; i++) {
            VulnerManger vulnerManger = new VulnerManger();
            vulnerManger.setId(idOrganizerService.getTaskId("漏洞信息"));
            vulnerManger.setOwAsset(owAssetArr[i]);
            vulnerManger.setType(loopholeCheck.getLoopholeType());
            vulnerManger.setLevel(loopholeCheck.getLoopholeLevel());
            vulnerManger.setName(loopholeCheck.getLoopholeDesc());
            vulnerManger.setOwFlow(flowManger.getId());
            vulnerManger.setCreateTime(ZonedDateTime.now());
            vulnerManger.setStatus("待处理");
            vulnerManger.setDept(user.get().getDept());
            vulnerManger.setOwnerId(user.get().getLogin());
            vulnerManger.setOwner(user.get().getUserName());
            vulnerManger.setFeedbackTime(String.valueOf(Instant.now().plus(1, ChronoUnit.DAYS)));
            vulnerMangerRepository.save(vulnerManger);
        }
        flowTodoService.flowMangerTodoSave(flowManger);
        mailOperation(flowManger);//漏洞流程操作记录and发送邮箱
        return flowManger.getId();
    }


    public Predicate getSpecification(SearchValue searchValue, String ggg, Root<VulnerManger> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        log.info("VulnerMangerServiceImpl to get getSpecification : searchValue：{}", searchValue);
        List<Predicate> predicates = new ArrayList<>(); //所有的断言
        if (StringUtils.isNotBlank(searchValue.getDept())) { //添加断言
            Predicate likeNickName = cb.like(root.get("dept").as(String.class), "%" + searchValue.getDept() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getType())) { //添加断言
            Predicate likeNickName = cb.like(root.get("type").as(String.class), "%" + searchValue.getType() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getFeedback())) { //添加断言
            Predicate likeNickName = cb.like(root.get("feedback").as(String.class), "%" + searchValue.getFeedback() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getStatus())) { //添加断言
            Predicate likeNickName = cb.like(root.get("status").as(String.class), "%" + searchValue.getStatus() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getName())) { //添加断言
            Predicate likeNickName = cb.like(root.get("name").as(String.class), "%" + searchValue.getName() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getOwAsset())) { //添加断言
            Predicate likeNickName = cb.like(root.get("owAsset").as(String.class), "%" + searchValue.getOwAsset() + "%");
            predicates.add(likeNickName);
        }
        List<Predicate> predicateOr = new ArrayList<>();
        if (StringUtils.isNotBlank(ggg)) { //添加断言
            predicateOr = getSpecificationOr(ggg, root, cb);
            predicates.add(cb.or(predicateOr.toArray(new Predicate[0])));
        }
        return cb.and(predicates.toArray(new Predicate[0]));
    }

    public List<Predicate> getSpecificationOr(String ggg, Root<VulnerManger> root, CriteriaBuilder cb) {
        log.info("VulnerMangerServiceImpl to get getSpecificationOr : ggg：{}", ggg);
        List<Predicate> predicateOr = new ArrayList<>();
        if (StringUtils.isNotBlank(ggg)) { //添加断言
            Predicate orDept = cb.like(root.get("dept").as(String.class), "%" + ggg + "%");
            Predicate orType = cb.like(root.get("type").as(String.class), "%" + ggg + "%");
            Predicate orFeedback = cb.like(root.get("feedback").as(String.class), "%" + ggg + "%");
            Predicate orId = cb.like(root.get("id").as(String.class), "%" + ggg + "%");
            Predicate orOwAsset = cb.like(root.get("owAsset").as(String.class), "%" + ggg + "%");
            Predicate orLevel = cb.like(root.get("level").as(String.class), "%" + ggg + "%");
            Predicate orDesc = cb.like(root.get("name").as(String.class), "%" + ggg + "%");
            Predicate orStatus = cb.like(root.get("status").as(String.class), "%" + ggg + "%");
            Predicate orEnv = cb.like(root.get("environment").as(String.class), "%" + ggg + "%");
            predicateOr.add(orDept);
            predicateOr.add(orType);
            predicateOr.add(orFeedback);
            predicateOr.add(orId);
            predicateOr.add(orOwAsset);
            predicateOr.add(orLevel);
            predicateOr.add(orDesc);
            predicateOr.add(orStatus);
            predicateOr.add(orEnv);

        }
        return predicateOr;
    }

    @Override
    public List<VulnerManger> findAll() {
        log.info("VulnerMangerServiceImpl to get findAll");
        return vulnerMangerRepository.findAll();
    }

    @Override
    public List<VulnerManger> findAllBySearch(String searchValue) {
        log.info("VulnerMangerServiceImpl to get findAllBySearch : searchValue：{}", searchValue);
        Specification<VulnerManger> specification = new Specification<VulnerManger>() {
            @Override
            public Predicate toPredicate(Root<VulnerManger> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                return cb.or(getSpecificationOr(searchValue, root, cb).toArray(new Predicate[0]));
            }
        };
        return this.vulnerMangerRepository.findAll(specification);
    }

    @Override
    public List<VulnerManger> findAllFilterByAll(String ggg, SearchValue searchValue) {
        log.info("VulnerMangerServiceImpl to get findAllFilterByAll : ggg：{},searchValue：{}", ggg, searchValue);
        Specification<VulnerManger> specification = new Specification<VulnerManger>() {
            @Override
            public Predicate toPredicate(Root<VulnerManger> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                return getSpecification(searchValue, ggg, root, query, cb);
            }
        };
        return this.vulnerMangerRepository.findAll(specification);
    }

    @Override
    public List<Integer> importTemplate(MultipartFile file, String param) throws Exception {
        log.info("VulnerMangerServiceImpl to get importTemplate : file：{}", file);

        //通过选择资产信息 从而获取到该资产下的属主信息。
        Map<String, Object> mapValue = new HashMap<String, Object>();
        mapValue = JSONObject.parseObject(param, Map.class);

        AssetApp assetApp = assetAppRepository.findOneByName((String) mapValue.get("owAsset"));
        String repeatTest = String.valueOf(mapValue.get("repeatTest"));
        if (repeatTest.equals("是")) {
            assetApp.setLastCheckTime(ZonedDateTime.now());
            assetApp = assetAppRepository.save(assetApp);
        }
        String owner = "";
        String ownerId = "";
        Optional<User> user;//获取到属主的部门信息
        if (mapValue.containsKey("safetyDetectionOwner")) {
            //判断是否包含这个主键，包含表示是安全检测导入的漏洞信息，那么属主信息就从安全检测中拿。
            String own = (String) mapValue.get("safetyDetectionOwner");//如果是安全检测导入的，那么默认获取安全检测的属主信息，如果是直接导入的漏洞那么就获取资产下面的属主信息
            owner = own.substring(own.indexOf("/") + 1);
            ownerId = own.substring(0, own.indexOf("/"));
            mapValue.put("owner", owner);
            mapValue.put("ownerId", ownerId);
            user = userRepository.findOneByLogin(ownerId);
        } else {
            //一般处理流程导入
            owner = String.valueOf(mapValue.get("owner"));
            ownerId = String.valueOf(mapValue.get("ownerId"));
            mapValue.put("owner", owner);
            mapValue.put("ownerId", ownerId);
            user = userRepository.findOneByLogin(ownerId);
        }

        if (null == file) {
            log.debug("VulnerMangerServiceImpl to get importTemplate -> 文件信息为空: file：{}", file);
            throw new RuntimeException("VulnerMangerServiceImpl to get importTemplate -> 文件信息为空");
        }

        InputStream inputStream = file.getInputStream();
        Map<String, String> flowTemplateDTOList = flowTemplateService.findFlowTemplateByInfo("导入", "漏洞管理");
        if (flowTemplateDTOList.size() <= 0) {
            log.debug("VulnerMangerServiceImpl to get importTemplate : flowTemplateDTOList：{}", flowTemplateDTOList);
            throw new RuntimeException("VulnerMangerServiceImpl to get importTemplate : flowTemplateDTOList：模板为空");
        }

        List<Map<String, Object>> list = FileUtils.readExcelFile(inputStream, file.getOriginalFilename(), null, flowTemplateDTOList);
        if (null != list && !list.isEmpty()) {
            List<VulnerManger> vulnerMangersList = null;
            try {
                vulnerMangersList = FlowBeanUtils.toObjectList(list, VulnerManger.class);
            } catch (Exception e) {
                log.error("VulnerMangerServiceImpl to get importTemplate : Exception：{}", e.getMessage());
                e.printStackTrace();
            }
            if (null != vulnerMangersList && vulnerMangersList.size() > 0) {
                FlowManger flowManger = addFlowMangerGetId(true, mapValue);//创建漏洞流程并返回id
                for (int i = 0; i < vulnerMangersList.size(); i++) {
                    VulnerManger vulnerManger = vulnerMangersList.get(i);//对象赋值
                    //安全检查导入漏洞进行过滤
                   /* if(mapValue.containsKey("applyType")){
                        String applyType=(String) mapValue.get("applyType");
                        if(!applyType.contains(vulnerManger.getType())){
                            continue;
                        }
                    }*/
                    try {
                        int number = i + 1;
                        if (isEmpty(vulnerManger.getType())) {
                            throw new RuntimeException("请检查第" + number + "行数据，必填项：漏洞类型为空 '" + vulnerManger.getType() + "' ！");
                        } else if (isEmpty(vulnerManger.getName())) {
                            throw new RuntimeException("请检查第" + number + "行数据，必填项：漏洞名称为空 '" + vulnerManger.getName() + "' ！");
                        }
                        String x = vulnerManger.getLevel();
                        boolean level = x == null ? false : "低危".equals(x) ? true : "中危".equals(x) ? true : "高危".equals(x) ? true : "严重".equals(x) ? true : false;
                        if (!level) {
                            throw new RuntimeException("请检查第" + number + "行数据，必填项：漏洞级别为空或级别填写错误 '" + vulnerManger.getLevel() + "' ！");
                        }
                        vulnerManger.setOwAsset((String) mapValue.get("owAsset"));
                        vulnerManger.setId(idOrganizerService.getTaskId("漏洞信息"));
                        vulnerManger.setDept(user.get().getDept());
                        vulnerManger.setStatus("待处理");
                        vulnerManger.setOwFlow(flowManger.getId());
                        vulnerManger.setOwner(owner);
                        vulnerManger.setOwnerId(ownerId);
                        vulnerManger.setCreateTime(ZonedDateTime.now());
                        vulnerManger.setEnvironment(setEnvironmentByAssetApp(assetApp.getEnvironment()));
                        vulnerManger.setFeedbackTime(String.valueOf(Instant.now().plus(1, ChronoUnit.DAYS)));
                        vulnerMangerRepository.save(vulnerManger);
                    } catch (RuntimeException e) {
                        log.error("VulnerMangerServiceImpl to get importTemplate : IOException：{}, ExceptionInformation", e.getMessage(), e);
                        throw new RuntimeException(e.getMessage());
                    } catch (Exception e) {
                        log.error("VulnerMangerServiceImpl to get importTemplate : ExceptionTwo：{}, ExceptionInformation", e.getMessage(), e);
                        throw new Exception(substring(e, 0, 50));
                    }
                }
                //当漏洞添加完成后获取到流程id,方便查看完成的安全检查对应的是那个漏洞流程。
                if ("安全检测".equals(mapValue.get("source")) && mapValue.containsKey("id")) {
                    String id = (String) mapValue.get("id");
                    if (!isEmpty(id)) {
                        Optional<SafetyDetection> assets = safetyDetectionRepository.findById((String) mapValue.get("id"));
                        if (assets.isPresent()) {
                            SafetyDetection safetyDetection = assets.get();
                            safetyDetection.setProcessId(flowManger.getId());
                            safetyDetectionRepository.save(safetyDetection);
                        }

                    }
                }
                flowTodoService.flowMangerTodoSave(flowManger);
                mailOperation(flowManger);//漏洞流程操作记录and发送邮箱
                return null;
            }
        }
        throw new IOException("Import excel is empty, please check!");
    }


    public FlowManger addFlowMangerGetId(boolean bool, Map<String, Object> map) {
        FlowManger flowManger = new FlowManger();
        flowManger.setId(idOrganizerService.getTaskId("漏洞流程"));
        if (bool) {
            flowManger.setOwAsset((String) map.get("owAsset"));
            flowManger.setSource((String) map.get("source"));
            //判断安全检测导入漏洞时是否包含附件
            if (map.containsKey("attachmentPath")) {
                flowManger.setAttachmentPath((String) map.get("attachmentPath"));
            }
        } else {
            flowManger.setOwAsset((String) map.get("owAsset"));
            if (map.containsKey("source")) {
                flowManger.setSource((String) map.get("source"));
                //flowManger.setSourceId((String) map.get("id"));
            } else {
                flowManger.setSource("手动新增");
            }
        }
        flowManger.setType("一般处理流程");
        flowManger.setStatus("处理中");
        if (map.containsKey("mangerId")) {//表示是漏洞排查过来的，从而将漏洞排查的管理员信息跟，审核员信息注入到漏洞流程中
            flowManger.setMangerId((String) map.get("mangerId"));
            flowManger.setManger((String) map.get("manger"));
            flowManger.setCheckerId((String) map.get("checkerId"));
            flowManger.setChecker((String) map.get("checker"));
        } else {
            UserDTO userDTO = userService.findByUserId(SecurityUtils.getCurrentUserLogin());
            flowManger.setMangerId(userDTO.getLogin());
            flowManger.setManger(userDTO.getUserName());
        }
        flowManger.setDescription(map.get("description").toString());
        if (map.containsKey("fixDeadlineTime")) {
            flowManger.setFixDeadlineTime(DateUtils.parse(map.get("fixDeadlineTime").toString()));
        } else {
            flowManger.setFixDeadlineTime(ZonedDateTime.now().plusDays(15));
        }
        flowManger.setOwnerId((String) map.get("ownerId"));
        flowManger.setOwner((String) map.get("owner"));
        flowManger.setCreateTime(ZonedDateTime.now());
        return flowMangerRepository.save(flowManger);
    }

    //判空。
    public Boolean isEmpty(String val) {
        if (null == val || "".equals(val)) {
            return true;
        }
        return false;
    }

    public void mailOperation(FlowManger flowManger) {
        FlowCodereview flowCodereview = new FlowCodereview();
        flowCodereview.setId(flowManger.getId());
        flowCodereview.setFlowStatus(flowManger.getStatus());
        flowCodereview.setApplicationName(flowManger.getOwAsset());
        flowCodereview.setFlowType(flowManger.getType());
        flowCodereview.setFlowSource(flowManger.getSource());
        flowCodereview.setOwner(flowManger.getOwner());
        flowCodereview.setPmManager(flowManger.getManger());
        flowCodereview.setOwnerId(flowManger.getOwnerId());
        flowCodereview.setPmManagerId(flowManger.getMangerId());
        flowCodereview.setApplicationName(flowManger.getOwAsset());
        //String[] arr2 = statusGetUser(flowManger);//获取当前状态下对应的角色
        flowCodereview.setCurrentOwnerId(flowManger.getCurrentOwnerId());
        flowCodereview.setCurrentOwner(flowManger.getCurrentOwner());
        flowCodereview.setExpectFinishTime(flowManger.getFixDeadlineTime());
        if (null != flowManger.getMangerId()) {
            flowCodereview.setCheckerId(flowManger.getCheckerId());
            flowCodereview.setChecker(flowManger.getChecker());
        }
        //还差应用资产
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(flowCodereview));
        Map<String, Object> map = new HashMap<>();
        String value = JsonUtil.stringify(jsonObject);
        map = JsonUtil.parse(value, Map.class);
        FlowProcessDTO flowProcessDTO = JSON.parseObject(JSON.toJSONString(map), FlowProcessDTO.class);
        flowHandleHistoryService.ProcessOperationRecordSave(flowProcessDTO, flowManger.getSource());//添加操作记录
        UserDTO user = userService.findByUserId(flowManger.getCurrentOwnerId());
        /*UserDTO user = userService.findByUserId(flowProcessDTO.getCurrentOwnerId());*/
        flowProcessDTO.setEmail(user.getEmail());
        flowProcessDTO.setRepairTime(DateUtils.parseZonedDateTime(flowCodereview.getExpectFinishTime()));
//        String domain = parameterConfigurationService.getParamValue("vsm", "domain");
//        flowProcessDTO.setDomain(domain);

        mailService.sendProcessFlowMail(flowProcessDTO);
    }

    @Override
    public VulnerManger vulnerHandling(VulnerManger vulnerManger) {
        VulnerManger vulnerManger2 = vulnerMangerRepository.findOneById(vulnerManger.getId());
        vulnerManger2.setStatus("已处理");
        vulnerManger2.setFeedback(vulnerManger.getFeedback());
        vulnerManger2.setFeedbackDesc(vulnerManger.getFeedbackDesc());
        vulnerManger2.setUpdateTime(ZonedDateTime.now());
        switch (vulnerManger.getFeedback()) {
            case "确认不涉及":
            case "已修复":
                vulnerManger2.setFeedbackTime(String.valueOf(Instant.now()));
                break;
            case "接受风险":
                vulnerManger2.setFeedbackTime(null);
                break;
            default:
                vulnerManger2.setFeedbackTime(vulnerManger.getFeedbackTime());
        }
        return vulnerMangerRepository.save(vulnerManger2);
    }


    //通过安全检测的状态获取对应的角色信息。
    public String[] statusGetUser(FlowManger flowManger) {
        String[] arr = new String[2];
        switch (flowManger.getStatus()) {
            case "审核中":
                arr[0] = flowManger.getCheckerId();
                arr[1] = flowManger.getChecker();
                break;
            case "处理中":
                arr[0] = flowManger.getOwnerId();
                arr[1] = flowManger.getOwner();
                break;
            case "新增":
            case "分配中":
            case "复核中":
            case "关闭":
                arr[0] = flowManger.getMangerId();
                arr[1] = flowManger.getManger();
                break;
            default:
                log.error("VulnerMangerServiceImpl -> statusGetUser", flowManger.getStatus());
        }
        return arr;
    }

}
