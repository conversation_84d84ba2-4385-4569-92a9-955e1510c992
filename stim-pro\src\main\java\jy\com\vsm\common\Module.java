package jy.com.vsm.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2021/1/20.
 */
public class Module {

    public static final int LOGIN = 1;
    public static final int DATA = 2;
    public static final int USER = 3;
    public static final int HOST = 4;
    public static final int SEEP = 5;
    public static final int CODE = 6;
    public static final int WEB = 7;
    public static final int MODEUL = 8;
    public static final int OTHER = 9;
    public static final int SAFE = 10;
    public static final int WARN = 11;
    public static final int IMPORT = 12;


    private static final Map<Integer, String> moudleMap = new HashMap<>();
    static {
        moudleMap.put(Module.LOGIN, "登录");
        moudleMap.put(Module.DATA, "业务数据");
        moudleMap.put(Module.IMPORT, "批量导入");
        moudleMap.put(Module.USER, "用户");
        moudleMap.put(Module.HOST, "主机漏洞");
        moudleMap.put(Module.SEEP, "渗漏测试");
        moudleMap.put(Module.CODE, "代码审计");
        moudleMap.put(Module.WEB, "WEB漏洞");
        moudleMap.put(Module.MODEUL, "组件漏洞");
        moudleMap.put(Module.OTHER, "第三方漏洞");
        moudleMap.put(Module.SAFE, "安全通知");
        moudleMap.put(Module.WARN, "风险提示");
    }

    public static String getNameByCode(int code) {
        return moudleMap.get(code);
    }
}
