package jy.com.vsm.domain;


import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * Permission defines what can be done by a user.
 * A permission object represents one permission record in database.
 */
@Entity
@Table(name = "jhi_permission")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Permission extends AbstractAuditingEntity implements Serializable, Comparable {

    @Id
    @GeneratedValue(generator="idGenerator")
    @GenericGenerator(name="idGenerator", strategy="uuid")
    private String id;

    @NotNull
    @Column(name = "domain", length = 100)
    private String domain;

    @NotNull
    @Column(name = "action", length = 100)
    private String action;

    @Column(name = "target", length = 100)
    private String target;

    @Column(name = "description", length = 255)
    private String description;
    /**
     * Store extra extraConfig info like link template
     */
    @Column(name = "config_json", length = 2000)
    private String configJson;



    @Transient
    private Map<String, Object> extraConfig = null;

    public Permission() {
    }


    @Override
    public int compareTo(Object o) {
        return this.toString().compareTo(((Permission) o).toString());
    }

    /**
     * Compare with id
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Permission that = (Permission) o;

        if (id != null ? !id.equals(that.id) : that.id != null) {
            return false;
        }

        return true;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Transient
    public String getPermkey() {
        StringBuilder sb = new StringBuilder(domain);
        if (StringUtils.isNotBlank(action)) {
            sb.append(":").append(action);
            if (StringUtils.isNotBlank(target)) {
                sb.append(":").append(target);
            }
        }
        return sb.toString();
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }


    @Override
    public String toString() {
        return "Permission{" +
            "id=" + id +
            ", domain='" + domain + '\'' +
            ", action='" + action + '\'' +
            ", target='" + target + '\'' +
            ", description='" + description + '\'' +
            ", configJson='" + configJson + '\'' +
            '}';
    }
}
