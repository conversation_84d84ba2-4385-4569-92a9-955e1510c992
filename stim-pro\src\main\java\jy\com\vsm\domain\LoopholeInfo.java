package jy.com.vsm.domain;

import lombok.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * loophole_info table mapping entity.
 *
 * <AUTHOR> 2021/07/17, created
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "loophole_info")
public class LoopholeInfo {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator = "idGenerator")
    @GenericGenerator(name = "idGenerator", strategy = "uuid")
    private String id;

    /**
     * iid
     */
    @Column(name = "iid")
    private String iid;

    /**
     * ruleID
     */
    @Column(name = "rule_id")
    private String ruleId;

    /**
     * 种类
     */
    @Column(name = "category")
    private String category;

    /**
     * 目录
     */
    @Column(name = "folder")
    private String folder;

    /**
     * 域
     */
    @Column(name = "kingdom")
    private String kingdom;

    /**
     * 摘要
     */
    @Column(name = "abstracts")
    private String abstracts;

    /**
     * 优先级
     */
    @Column(name = "friority")
    private String friority;

    /**
     * 主要
     */
    @Column(name = "primary_json")
    private String primaryJson;

    /**
     * 来源
     */
    @Column(name = "source_json")
    private String sourceJson;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIid() {
        return iid;
    }

    public void setIid(String iid) {
        this.iid = iid;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }

    public String getKingdom() {
        return kingdom;
    }

    public void setKingdom(String kingdom) {
        this.kingdom = kingdom;
    }

    public String getAbstracts() {
        return abstracts;
    }

    public void setAbstracts(String abstracts) {
        this.abstracts = abstracts;
    }

    public String getFriority() {
        return friority;
    }

    public void setFriority(String friority) {
        this.friority = friority;
    }

    public String getPrimaryJson() {
        return primaryJson;
    }

    public void setPrimaryJson(String primaryJson) {
        this.primaryJson = primaryJson;
    }

    public String getSourceJson() {
        return sourceJson;
    }

    public void setSourceJson(String sourceJson) {
        this.sourceJson = sourceJson;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "LoopholeInfo{" +
            "id='" + id + '\'' +
            ", iid='" + iid + '\'' +
            ", ruleId='" + ruleId + '\'' +
            ", category='" + category + '\'' +
            ", folder='" + folder + '\'' +
            ", kingdom='" + kingdom + '\'' +
            ", abstracts='" + abstracts + '\'' +
            ", friority='" + friority + '\'' +
            ", primaryJson='" + primaryJson + '\'' +
            ", sourceJson='" + sourceJson + '\'' +
            ", createTime=" + createTime +
            '}';
    }
}
