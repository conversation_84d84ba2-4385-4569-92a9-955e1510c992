package jy.com.vsm.common;

import lombok.Getter;


@Getter
public enum LevelEnum {

    // 1:低 2:中 3:高 4:提示
    LOW(1, "TIPS", "低危"),
    MIDDLE(2, "LOW", "中危"),
    HIGH(3, "MID<PERSON><PERSON>", "高危"),
    CRITICAL(4, "HIGH", "严重");


    private int code;
    private String level;
    private String desc;

    LevelEnum(int code, String level, String desc) {
        this.code = code;
        this.level = level;
        this.desc = desc;
    }

    // 普通方法
    public static String getDesc(int code) {
        for (LevelEnum levelEnum : LevelEnum.values()) {
            if (levelEnum.getCode() == code) {
                return levelEnum.desc;
            }
        }
        return null;
    }


    // 普通方法
    public static int getCodeByLevel(String level) {
        for (LevelEnum levelEnum : LevelEnum.values()) {
            if (levelEnum.getLevel().equals(level)) {
                return levelEnum.code;
            }
        }
        return 1;
    }


}
