package jy.com.vsm.domain;

import lombok.Data;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.ZonedDateTime;

/*
 * 类说明：参数配置
 * @ClassName ParameterConfiguration
 * @Date 2021/07/12 15:49
 **/
@Data
@Entity
@Table(name = "parameter_configuration")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ParameterConfiguration implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id @GeneratedValue(generator="system-uuid")
    @GenericGenerator(name="system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "param_body")
    private String paramBody;

    @Column(name = "param_name")
    private String paramName;

    @Column(name = "param_value")
    private String paramValue;

    @Column(name = "param_desc")
    private String paramDesc;

    @Column(name = "status")
    private String status;

    @Column(name = "param_format")
    private String paramFormat;

    @Column(name = "create_name")
    private String createName;

    @Column(name = "update_name")
    private String updateName;

    @Column(name = "create_time")
    private ZonedDateTime createTime;

    @Column(name = "update_time")
    private ZonedDateTime updateTime;

    @Column(name = "encrypted")
    private String encrypted;

}
