package jy.com.vsm.domain;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A PathConfig.
 */
@Entity
@Table(name = "path_config")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class PathConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "service_address")
    private String add;

    @Column(name = "port")
    private Long port;

    @Column(name = "state")
    private String state;

    @Column(name = "product")
    private String product;

    @Column(name = "username")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "script_path")
    private String scriptPath;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAdd() {
        return add;
    }

    public PathConfig add(String add) {
        this.add = add;
        return this;
    }

    public void setAdd(String add) {
        this.add = add;
    }

    public Long getPort() {
        return port;
    }

    public PathConfig port(Long port) {
        this.port = port;
        return this;
    }

    public void setPort(Long port) {
        this.port = port;
    }

    public String getState() {
        return state;
    }

    public PathConfig state(String state) {
        this.state = state;
        return this;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getProduct() {
        return product;
    }

    public PathConfig product(String product) {
        this.product = product;
        return this;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getScriptPath() {
        return scriptPath;
    }

    public void setScriptPath(String scriptPath) {
        this.scriptPath = scriptPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PathConfig pathConfig = (PathConfig) o;
        if (pathConfig.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pathConfig.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "PathConfig{" +
            "id=" + id +
            ", add='" + add + '\'' +
            ", port=" + port +
            ", state='" + state + '\'' +
            ", product='" + product + '\'' +
            ", username='" + username + '\'' +
            ", password='" + password + '\'' +
            ", scriptPath='" + scriptPath + '\'' +
            '}';
    }
}
