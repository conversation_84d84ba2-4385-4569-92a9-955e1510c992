package jy.com.vsm.domain.flow;

import lombok.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * A FlowController.
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "flow_controller")
public class FlowController implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator="idGenerator")
    @GenericGenerator(name="idGenerator", strategy="uuid")
    private String id;

    @Column(name = "flow_status")
    private String flowStatus;

    @Column(name = "flow_next_status")
    private String flowNextStatus;

    @Column(name = "flow_prev_status")
    private String flowPrevStatus;

    @Column(name = "flow_role")
    private String flowRole;

    @Column(name = "create_time")
    private ZonedDateTime createTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "type")
    private String type;

}
