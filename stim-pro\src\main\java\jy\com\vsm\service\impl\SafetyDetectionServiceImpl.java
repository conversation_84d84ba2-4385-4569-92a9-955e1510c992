package jy.com.vsm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import io.micrometer.core.instrument.util.StringUtils;
import jy.com.vsm.domain.SafetyDetection;
import jy.com.vsm.domain.User;
import jy.com.vsm.domain.VulnerManger;
import jy.com.vsm.domain.flow.FlowController;
import jy.com.vsm.repository.AssetAppRepository;
import jy.com.vsm.repository.SafetyDetectionRepository;
import jy.com.vsm.repository.UserRepository;
import jy.com.vsm.security.SecurityUtils;
import jy.com.vsm.service.*;
import jy.com.vsm.service.dto.FlowProcessDTO;
import jy.com.vsm.service.dto.SortDTO;
import jy.com.vsm.service.page.SearchValue;
import jy.com.vsm.service.util.ServiceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.ZonedDateTime;
import java.util.*;

@Service
@Transactional
public class SafetyDetectionServiceImpl implements SafetyDetectionService {

    private final Logger log = LoggerFactory.getLogger(SafetyDetectionServiceImpl.class);

    @Autowired
    private IdOrganizerService idOrganizerService;

    @Autowired
    private FlowControllerService flowControllerService;

    @Autowired
    private MailService mailService;

    @Autowired
    private FlowTodoService flowTodoService;


    @Autowired
    private AssetAppService assetAppService;

    @Autowired
    private FlowHandleHistoryService flowHandleHistoryService;

    @Resource
    private UserService userService;

    private final UserRepository userRepository;

    private final SafetyDetectionRepository safetyDetectionRepository;


    public SafetyDetectionServiceImpl(UserRepository userRepository, SafetyDetectionRepository safetyDetectionRepository) {
        this.userRepository = userRepository;
        this.safetyDetectionRepository = safetyDetectionRepository;
    }

    @Override
    public Page<SafetyDetection> findAllFilter(int start, int length, String searchValue, SortDTO... sortDTOs) {
        log.info("SafetyDetectionServiceImpl to get findAllFilter :searchValue：{},sortDTOs：{}", searchValue, sortDTOs);
        Specification<SafetyDetection> specification = new Specification<SafetyDetection>() {
            @Override
            public Predicate toPredicate(Root<SafetyDetection> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicateAnd = new ArrayList<>();
                List<String> roles = userService.getCurrentUserRole();
                if (!roles.contains("ROLE_ADMIN") && !roles.contains("ROLE_ADMIN_VSM") && !roles.contains("ROLE_SECURITY")) {
                    Predicate userPredicate = ServiceUtil.queryFlowByUserRole(root, cb, "safety");
                    if (userPredicate != null) {
                        predicateAnd.add(userPredicate);
                    }
                }
                if (StringUtils.isNotEmpty(searchValue)) {
                    Predicate pedicate = cb.or(getSpecificationOr(searchValue, root, cb).toArray(new Predicate[0]));
                    predicateAnd.add(pedicate);

                }
                return cb.and(predicateAnd.toArray(new Predicate[0]));
            }
        };
        Pageable pageable = ServiceUtil.getPageable(start, length, sortDTOs);
        return this.safetyDetectionRepository.findAll(specification, pageable);
    }

    @Override
    public Page<SafetyDetection> findAllFilter(int start, int length, String ggg, SearchValue searchValue, SortDTO... sortDTOs) {
        log.info("SafetyDetectionServiceImpl to get findAllFilter : ggg：{},searchValue：{},sortDTOs：{}", ggg, searchValue, sortDTOs);
        Specification<SafetyDetection> specification = new Specification<SafetyDetection>() {
            @Override
            public Predicate toPredicate(Root<SafetyDetection> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicateAnd = new ArrayList<>();
                List<String> roles = userService.getCurrentUserRole();
                if (!roles.contains("ROLE_ADMIN") && !roles.contains("ROLE_ADMIN_VSM") && !roles.contains("ROLE_SECURITY")) {
                    Predicate userPredicate = ServiceUtil.queryFlowByUserRole(root, cb, "safety");
                    if (userPredicate != null) {
                        predicateAnd.add(userPredicate);
                    }
                }
                Predicate predicate = getSpecification(searchValue, ggg, root, query, cb);
                predicateAnd.add(predicate);
                return cb.and(predicateAnd.toArray(new Predicate[0]));
            }

        };
        Pageable pageable = ServiceUtil.getPageable(start, length, sortDTOs);
        return this.safetyDetectionRepository.findAll(specification, pageable);
    }

    public Predicate getSpecification(SearchValue searchValue, String ggg, Root<SafetyDetection> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        log.info("SafetyDetectionServiceImpl to get getSpecification : searchValue：{}", searchValue);
        List<Predicate> predicates = new ArrayList<>(); //所有的断言
        if (StringUtils.isNotBlank(searchValue.getSystemName())) { //添加断言
            Predicate likeNickName = cb.like(root.get("systemName").as(String.class), "%" + searchValue.getSystemName() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getApplyType())) { //添加断言
            Predicate likeNickName = cb.like(root.get("applyType").as(String.class), "%" + searchValue.getApplyType() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getTestingType())) { //添加断言
            Predicate likeNickName = cb.like(root.get("testingType").as(String.class), "%" + searchValue.getTestingType() + "%");
            predicates.add(likeNickName);
        }
        if (StringUtils.isNotBlank(searchValue.getTestingEnvironment())) { //添加断言
            Predicate likeNickName = cb.like(root.get("testingEnvironment").as(String.class), "%" + searchValue.getTestingEnvironment() + "%");
            predicates.add(likeNickName);
        }
        List<Predicate> predicateOr = new ArrayList<>();
        if (StringUtils.isNotBlank(ggg)) { //添加断言
            predicateOr = getSpecificationOr(ggg, root, cb);
            predicates.add(cb.or(predicateOr.toArray(new Predicate[0])));
        }
        return cb.and(predicates.toArray(new Predicate[0]));
    }

    public List<Predicate> getSpecificationOr(String ggg, Root<SafetyDetection> root, CriteriaBuilder cb) {
        log.info("SafetyDetectionServiceImpl to get getSpecificationOr : ggg：{}", ggg);
        List<Predicate> predicateOr = new ArrayList<>();
        if (StringUtils.isNotBlank(ggg)) { //添加断言
            Predicate orSystemName = cb.like(root.get("systemName").as(String.class), "%" + ggg + "%");
            Predicate orApplyType = cb.like(root.get("applyType").as(String.class), "%" + ggg + "%");
            Predicate orTestingType = cb.like(root.get("testingType").as(String.class), "%" + ggg + "%");
            Predicate orId = cb.like(root.get("id").as(String.class), "%" + ggg + "%");
            Predicate orTestingEnvironment = cb.like(root.get("testingEnvironment").as(String.class), "%" + ggg + "%");
            Predicate orStatus = cb.like(root.get("status").as(String.class), "%" + ggg + "%");

            predicateOr.add(orSystemName);
            predicateOr.add(orApplyType);
            predicateOr.add(orTestingType);
            predicateOr.add(orId);
            predicateOr.add(orTestingEnvironment);
            predicateOr.add(orStatus);
        }
        return predicateOr;
    }

    public List<SafetyDetection> getAllSafetyDetection() {
        return safetyDetectionRepository.findAll();
    }

    @Override
    public SafetyDetection save(SafetyDetection safetyDetection) {
        Optional<User> user = userRepository.findOneByLogin(SecurityUtils.getCurrentUserLogin());
        safetyDetection.setOwner(user.get().getUserName());
        safetyDetection.setOwnerId(user.get().getLogin());
        safetyDetection.setDept(user.get().getDept());
        safetyDetection.setStatus("审核中");
        if (null == safetyDetection.getId()) {
            safetyDetection.setId(idOrganizerService.getTaskId("安全检测"));
            safetyDetection.setCreateTime(ZonedDateTime.now());
            safetyDetection.setType("安全检测");
        } else {
            safetyDetection.setUpdateTime(ZonedDateTime.now());
        }
        SafetyDetection safetyDetection2 = safetyDetectionRepository.save(safetyDetection);
        flowTodoService.safetyDetectionTodoSave(safetyDetection2);
        flowHandleHistory(safetyDetection2, null, "待分配");//操作日志
        sendMailbox(safetyDetection2, null);
        return safetyDetection2;
    }

    @Override
    public void delete(String id) {
        log.debug("SafetyDetectionServiceImpl Request to delete SafetyDetection : {}", id);
        safetyDetectionRepository.deleteById(id);
        flowTodoService.deleteByFlowId(id);
        flowHandleHistoryService.deleteByFlowId(id);
    }

    @Override
    public SafetyDetection findOne(String id) {
        log.info("SafetyDetectionServiceImpl to get findOne : id：{}", id);
        Optional<SafetyDetection> assets = safetyDetectionRepository.findById(id);
        return assets.get();
    }

    //流程执行
    @Override
    public Map<String, Object> detectionProcess(SafetyDetection safetyDetection) throws RuntimeException {
        FlowController flowController = flowControllerService.findOneByStatus(safetyDetection.getStatus(), "安全检测");
        Optional<SafetyDetection> safetyDetection2 = safetyDetectionRepository.findById(safetyDetection.getId());//获取到检查信息将流程id存入后save
        String currentStatus = safetyDetection.getStatus();
        safetyDetection.setStatus(flowController.getFlowNextStatus());
        safetyDetection.setUpdateTime(ZonedDateTime.now());
        //Todo 看不懂
        safetyDetection.setProcessId(safetyDetection2.get().getProcessId());

        SafetyDetection safetyDetection3 = safetyDetectionRepository.save(safetyDetection);//保存漏洞流程信息
        flowTodoService.safetyDetectionTodoSave(safetyDetection3);
        flowHandleHistory(safetyDetection3, null, currentStatus);//操作日志
        assetAppService.updateAppCheckTime(safetyDetection.getSystemName());
        if (!"完成".equals(flowController.getFlowNextStatus())) {
            sendMailbox(safetyDetection3, null);
        }
        Map map = new HashMap();
        map.put("code", "200");
        map.put("data", safetyDetection3);
        return map;
    }

    //回退
    @Override
    public Map<String, Object> detectionGiveBack(JSONObject jsonObject) throws RuntimeException {
        String param = jsonObject.get("param").toString();
        String callBack = jsonObject.get("callBack").toString();
        SafetyDetection safetyDetection = JSONObject.parseObject(param, new TypeReference<SafetyDetection>() {
        });
        FlowController flowController = flowControllerService.findOneByStatus(safetyDetection.getStatus(), "安全检测");
        safetyDetection.setStatus(flowController.getFlowPrevStatus());
        safetyDetection.setUpdateTime(ZonedDateTime.now());
        SafetyDetection safetyDetection2 = safetyDetectionRepository.save(safetyDetection);//保存漏洞流程信息
        flowTodoService.safetyDetectionTodoSave(safetyDetection2);//添加待办
        flowHandleHistory(safetyDetection2, callBack, safetyDetection.getStatus());//操作日志
        sendMailbox(safetyDetection2, callBack);
        Map map = new HashMap();
        map.put("code", "200");
        map.put("data", safetyDetection2);
        return map;
    }

    @Override
    public List<Map<String, Object>> countFlowByCreateTime() {
        return safetyDetectionRepository.countFlowByCreateTime();
    }

    //组装发送邮箱信息
    public Map<String, Object> sendMailbox(SafetyDetection safetyDetection, String callBack) {
        //发送邮箱操作
        try {
            String[] arr = statusGetUser(safetyDetection, safetyDetection.getStatus());//获取当前状态下对应的角色
            Optional<User> userOptional = userRepository.findOneByLogin(arr[0]);
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                FlowProcessDTO flowProcessDTO = new FlowProcessDTO();
                flowProcessDTO.setEmail(user.getEmail());
                flowProcessDTO.setId(safetyDetection.getId());
                flowProcessDTO.setFlowStatus(safetyDetection.getStatus());
                flowProcessDTO.setApplicationName(safetyDetection.getSystemName());
                flowProcessDTO.setFlowSource(safetyDetection.getApplyType());
                flowProcessDTO.setFlowType("安全检测申请");
                String[] arr2 = statusGetUser(safetyDetection, safetyDetection.getStatus());//获取当前状态下对应的角色
                flowProcessDTO.setCurrentOwnerId(arr2[0]);
                flowProcessDTO.setCurrentOwner(arr2[1]);
                flowProcessDTO.setOwner(safetyDetection.getOwner());
                flowProcessDTO.setOwnerId(safetyDetection.getOwnerId());

                //检测类型
                flowProcessDTO.setFlowAdress(safetyDetection.getTestingType());
                //检测环境
                flowProcessDTO.setFlowName(safetyDetection.getTestingEnvironment());
                flowProcessDTO.setFlowDesc(safetyDetection.getDescription());
                if (StringUtils.isNotEmpty(callBack)) {
                    flowProcessDTO.setCallBackDesc(callBack);
                    mailService.sendSafatyDetectionMailByCategory(flowProcessDTO, "callbackSafeCheck", "callback");
                } else {
                    mailService.sendSafatyDetectionMailByCategory(flowProcessDTO, "processedSafeCheck", "process");
                }
                Map map = new HashMap();
                map.put("code", "200");
                map.put("data", safetyDetection);
                return map;
            }
        } catch (Exception e) {
            log.error("SafetyDetectionServiceImpl -> sendMailbox error:{}", e.getMessage());
        }
        return null;
    }

    /**
     * @MethodDescriptor: 组装操作记录数据入库
     * @Param: [SafetyDetection]
     * @Return: void
     **/
    public void flowHandleHistory(SafetyDetection safetyDetection, String callBack, String UpAndDownStep) throws RuntimeException {
        try {
            FlowProcessDTO flowProcessDTO = new FlowProcessDTO();
            flowProcessDTO.setId(safetyDetection.getId());
            flowProcessDTO.setFlowStatus(safetyDetection.getStatus());

            String[] arr = statusGetUser(safetyDetection, safetyDetection.getStatus());//获取当前状态下对应的角色
            flowProcessDTO.setOwnerId(arr[0]);//当前
            flowProcessDTO.setOwner(arr[1]);
            //safetyDetection.setStatus(UpAndDownStep);//原状态
            String[] arr2 = statusGetUser(safetyDetection, UpAndDownStep);//获取当前状态下对应的角色
            flowProcessDTO.setPmManagerId(arr2[0]);//上下步骤
            flowProcessDTO.setPmManager(arr2[1]);
            //safetyDetection.setStatus(cz);//当状态重置后，需要重置回来，不然最后执行添加操作的状态就会有问题。

            if (null != callBack) {
                flowProcessDTO.setCallBackDesc(callBack);
                flowHandleHistoryService.ProcessOperationRecordSave(flowProcessDTO);
            } else {
                flowHandleHistoryService.ProcessOperationRecordSave(flowProcessDTO);
            }
        } catch (Exception e) {
            log.error("SafetyDetectionServiceImpl -> flowHandleHistory error:{}", e.getMessage());
            throw new RuntimeException("组装历史操作历史记录失败 异常信息：" + e.getMessage());
        }
    }

    //通过安全检测的状态获取对应的角色信息。
    public String[] statusGetUser(SafetyDetection safetyDetection, String status) {
        String[] arr = new String[2];
        switch (status) {
            case "审核中":
                arr[0] = safetyDetection.getManagerId();
                arr[1] = safetyDetection.getManager();
                break;

            case "待测试":
                arr[0] = safetyDetection.getSecurityCheckerLeaderId();
                arr[1] = safetyDetection.getSecurityCheckerLeader();
                break;

            case "完成":
                arr[0] = safetyDetection.getSecurityCheckerLeaderId();
                arr[1] = safetyDetection.getSecurityCheckerLeader();
                break;
            case "待审批":
                arr[0] = safetyDetection.getLineLeaderId();
                arr[1] = safetyDetection.getLineLeader();
                break;
            case "安全审核":
                arr[0] = safetyDetection.getSecurityLeaderId();
                arr[1] = safetyDetection.getSecurityLeader();
                break;
            case "待分配":
                arr[0] = safetyDetection.getOwnerId();
                arr[1] = safetyDetection.getOwner();
                break;
            default:
                log.error("SafetyDetectionServiceImpl -> statusGetUser", safetyDetection.getStatus());
        }
        return arr;
    }

}
