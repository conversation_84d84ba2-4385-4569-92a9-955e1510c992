package jy.com.vsm.common;

public enum LeakCategoryEnum {

    HOST("host", "主机漏洞"),
    NETWORK("network", "网络设备漏洞"),
    SYSTEM("system", "系统漏洞"),
    MODULE("module", "应用组件漏洞");

    private String code;
    private String name;

    // 构造方法
    private LeakCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 普通方法
    public static String getCode(String name) {
        for (LeakCategoryEnum leakCategoryEnum : LeakCategoryEnum.values()) {
            if (leakCategoryEnum.getName().equals(name)) {
                return leakCategoryEnum.code;
            }
        }
        return null;
    }

    // get set 方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
