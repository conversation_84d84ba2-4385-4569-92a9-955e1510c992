package jy.com.vsm.common;

/**
 * <AUTHOR> 2021/1/20.
 */
public enum ModuleEnum {

    HOST("主机漏洞"),
    SEEPTEST("渗漏测试"),
    CODEREVIEW("代码审计"),
    WEB("WEB漏洞"),
    MODEULE("组件漏洞"),
    OTHER("第三方漏洞"),
    SAFE("安全通知"),
    WARN("风险提示");

    private String desc;
    private ModuleEnum(String desc){
        this.desc=desc;
    }

    @Override
    public String toString() {
        return desc;
    }

}
