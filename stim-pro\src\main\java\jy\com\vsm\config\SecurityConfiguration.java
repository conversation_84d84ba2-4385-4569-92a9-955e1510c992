package jy.com.vsm.config;

import jy.com.vsm.security.AuthoritiesConstants;
import jy.com.vsm.security.jwt.JWTConfigurer;
import jy.com.vsm.security.jwt.JWTFilter;
import jy.com.vsm.security.jwt.TokenProvider;
import jy.com.vsm.service.PersistentTokenService;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.filter.CorsFilter;

@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfiguration {


    private final TokenProvider tokenProvider;

    private final PersistentTokenService persistentTokenService;

    private final CorsFilter corsFilter;

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    private final JWTFilter jwtFilter;

    public SecurityConfiguration(
        TokenProvider tokenProvider,
        CorsFilter corsFilter, JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint,
        PersistentTokenService persistentTokenService, JWTFilter jwtFilter) {

        this.tokenProvider = tokenProvider;
        this.corsFilter = corsFilter;
        this.jwtAuthenticationEntryPoint = jwtAuthenticationEntryPoint;
        this.persistentTokenService = persistentTokenService;
        this.jwtFilter = jwtFilter;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }


    @Bean
    public AjaxLogoutSuccessHandler ajaxLogoutSuccessHandler() {
        return new AjaxLogoutSuccessHandler();
    }


    /**
     * 获取AuthenticationManager（认证管理器），登录时认证使用
     * 默认认证
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }


    @Bean
    public JWTConfigurer securityConfigurerAdapter() {
        return new JWTConfigurer(tokenProvider, persistentTokenService);
    }


    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
            .exceptionHandling()
            .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class)
            .exceptionHandling()
            .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .rememberMe()
            .rememberMeParameter("remember-me")
            .and()
            .headers()
            .frameOptions()
            .disable()
            .and()
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
            // 👉 静态资源放行
            .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
            .antMatchers("/app/**/*.{js,html}").permitAll()
            .antMatchers("/bower_components/**").permitAll()
            .antMatchers("/i18n/**").permitAll()
            .antMatchers("/content/**").permitAll()
            .antMatchers("/test/**").permitAll()

            .antMatchers("/api/activate").permitAll()
            .antMatchers("/api/authenticate").permitAll()
            .antMatchers("/api/sso/login/**").permitAll()
            .antMatchers("/api/flow/todo/oa").permitAll()
            .antMatchers("/api/asset/asset-middleware/qingtengyun/save").permitAll()
            .antMatchers("/api/account/reset_password/init").permitAll()
            .antMatchers("/api/account/reset_password/finish").permitAll()
            .antMatchers("/api/v2/vulnerability-models/**").permitAll() // 临时允许访问漏洞模型API
            .antMatchers("/api/admin/flow-handle-histories/timeline/**").authenticated()
            .antMatchers("/api/report/flow-report/line/type").authenticated()
            .antMatchers("/api/report/flow-report/line/type/*").authenticated()
            .antMatchers("/api/asset/asset-applications").authenticated()
            .antMatchers("/api/admin/users/role/**").authenticated()
            .antMatchers("/api/admin/flow-template/search").authenticated()
            .antMatchers("/api/admin/**").hasAuthority(AuthoritiesConstants.ADMIN)
            .antMatchers("/api/asset/**").authenticated()
            .antMatchers("/api/report/**").authenticated()
            .antMatchers("/api/flow/**").authenticated()
            .antMatchers("/api/**").authenticated()
            .antMatchers("/management/health").permitAll()
            .antMatchers("/management/**").hasAuthority(AuthoritiesConstants.ADMIN)
            .and()
            .httpBasic()
            .and()
            .apply(securityConfigurerAdapter())
            .and()
            .logout()
            .logoutUrl("/logout")
            .logoutSuccessHandler(ajaxLogoutSuccessHandler())
            .permitAll();  // 配置注销和注销成功处理器

        return http.build();
    }

}
